/**
 * Filter Tabs Functionality for FamousTube-style theme
 */
(function($) {
    'use strict';

    $(document).ready(function() {
        
        // Handle filter tab clicks
        $('.filter-tabs .filter-list li a').on('click', function(e) {
            e.preventDefault();
            
            var $this = $(this);
            var filter = $this.data('filter');
            var $contentLoop = $('#recent-content');
            
            // Update active tab
            $('.filter-tabs .filter-list li').removeClass('active');
            $this.parent().addClass('active');
            
            // Show loading state
            $contentLoop.addClass('loading');
            $contentLoop.html('<div class="loading-spinner">Loading...</div>');
            
            // Make AJAX request to get filtered content
            $.ajax({
                url: blackvideo_ajax.ajax_url,
                type: 'POST',
                data: {
                    action: 'blackvideo_filter_posts',
                    filter: filter,
                    nonce: blackvideo_ajax.nonce
                },
                success: function(response) {
                    if (response.success) {
                        $contentLoop.removeClass('loading');
                        $contentLoop.html(response.data);
                        
                        // Trigger any necessary events after content load
                        $(document).trigger('blackvideo_content_loaded');
                    } else {
                        console.error('Filter request failed:', response.data);
                        $contentLoop.removeClass('loading');
                        $contentLoop.html('<p>Error loading content. Please try again.</p>');
                    }
                },
                error: function(xhr, status, error) {
                    console.error('AJAX error:', error);
                    $contentLoop.removeClass('loading');
                    $contentLoop.html('<p>Error loading content. Please try again.</p>');
                }
            });
        });
        
        // Add loading styles
        if (!$('#filter-loading-styles').length) {
            $('head').append(`
                <style id="filter-loading-styles">
                .content-loop.loading {
                    opacity: 0.6;
                    pointer-events: none;
                }
                .loading-spinner {
                    text-align: center;
                    padding: 40px;
                    color: #fff;
                    font-size: 16px;
                }
                .loading-spinner:before {
                    content: "";
                    display: inline-block;
                    width: 20px;
                    height: 20px;
                    border: 2px solid #ff9800;
                    border-radius: 50%;
                    border-top-color: transparent;
                    animation: spin 1s linear infinite;
                    margin-right: 10px;
                    vertical-align: middle;
                }
                @keyframes spin {
                    to { transform: rotate(360deg); }
                }
                </style>
            `);
        }
    });

})(jQuery);
