/* Responsive CSS Style */
@media only screen and (min-width: 1550px) {
  .entry-related .hentry .thumbnail-link {
    width: 150px; } }
@media only screen and (max-width: 1549px) {
  .container {
    width: 100%; }

  .single-wrap {
    width: 94%; }

  #primary {
    width: 70%; }

  .sidebar {
    width: 28%; } }
@media only screen and (min-width: 1120px) and (max-width: 1550px) {
  .header-search {
    width: 300px;
    margin-left: 0;
    left: 32px;
    position: relative; } }
@media only screen and (min-width: 959px) and (max-width: 1119px) {
  .container {
    width: 100%; }

  .site-branding {
    min-width: auto; }

  .header-search {
    width: 220px;
    margin-left: 0;
    left: 32px;
    position: relative; }

  #primary-menu li a {
    font-size: 13px; }

  #primary {
    width: 70%; }

  .sidebar {
    width: 28%; } }
/* Smaller than standard 960 (devices and browsers) */
@media only screen and (max-width: 959px) {
  .admin-bar .site-header {
    top: 0; }

  .header-toggles {
    display: block;
    right: 2%; }

  .left-sidebar {
    display: none; }

  .home .site-content:before,
  .archive .site-content:before,
  .search .site-content:before {
    content: none; }

  .single-wrap,
  .content-wrap {
    margin-left: 0;
    margin-right: 0;
    margin-bottom: 0;
    padding-top: 0;
    padding-left: 0;
    padding-right: 0; }

  .site-content:before {
    display: none;
    content: none; }

  .site-header {
    height: 60px;
    position: relative; }
    .site-header .search-icon {
      margin-right: 6px; }

  .site-start {
    position: relative; }

  .search-icon {
    display: block; }

  .header-search {
    border: 1px solid #e5e5e5;
    display: none;
    height: 52px;
    width: 100%;
    top: 60px;
    left: 0;
    padding: 0; }
    .header-search .search-input {
      width: 100%;
      height: 50px;
      line-height: 50px; }
    .header-search .search-submit {
      background: none;
      border-left: none;
      right: 0; }

  #page {
    width: 100%; }

  .container {
    width: 96%; }

  #primary,
  #secondary {
    width: 100%;
    margin-left: auto;
    margin-right: auto; }

  .site-footer {
    width: 100%; }

  #primary-bar,
  #secondary-nav {
    display: none; }

  .site-branding {
    padding-left: 0;
    margin-left: 2%; }
    .site-branding #logo {
      height: 60px;
      line-height: 60px;
      margin: 0; }
    .site-branding img {
      width: auto; }
    .site-branding .site-title {
      font-size: 1.2em;
      line-height: 60px; }

  #primary-nav {
    display: none; }

  #primary {
    float: none; }

  #secondary {
    float: none;
    margin-top: 20px; }

  .sidebar .widget_ad,
  .site-footer .widget_ad {
    text-align: center; }

  #site-bottom {
    text-align: center; }
    #site-bottom .site-info {
      float: none;
      margin-bottom: 5px; }
    #site-bottom .footer-nav {
      float: none; }
      #site-bottom .footer-nav ul li {
        padding: 0 5px; }

  #back-top a span {
    bottom: 10px; }

  .archive .breadcrumbs .breadcrumbs-nav,
  .search .breadcrumbs .breadcrumbs-nav {
    display: none; }

  .breadcrumbs .breadcrumbs-nav {
    margin-bottom: 12px; }

  .single-wrap {
    width: 100%; }

  .pagination {
    margin-bottom: 0; } }
/* Tablet Portrait size to standard 960 (devices and browsers) */
@media only screen and (min-width: 768px) and (max-width: 959px) {
  .home .site-content {
    margin-top: 16px; }

  .site-content {
    margin-top: 18px; } }
/* All Mobile Sizes (devices and browser) */
@media only screen and (max-width: 767px) {
  .site-branding img {
    max-height: 42px;
    width: auto; }
  .site-branding .site-title {
    line-height: 60px;
    margin: 0; }
  .site-branding .site-description {
    display: none; }

  .mobile-menu-icon {
    right: 50px; }

  .content-block {
    margin-bottom: 10px; }

  .sidebar {
    margin-top: 20px; }

  #recent-content .widget_media_image,
  #recent-content .widget_custom_html {
    margin-bottom: 15px; }

  .content-block .section-heading .taxonomy-description,
  .breadcrumbs .taxonomy-description {
    display: none; }

  .single .entry-header .entry-meta {
    float: none; }

  .error-404 .page-content .search-form input.search-field,
  .search-no-results .page-content .search-form input.search-field {
    width: 170px; }

  .comment-form .comment-form-author,
  .comment-form .comment-form-email,
  .comment-form .comment-form-url {
    width: 100%; }

  #site-bottom .footer-nav li {
    border-right: none;
    margin: 0 5px;
    padding: 0; } }
/* Mobile Landscape Size to Tablet Portrait (devices and browsers) */
@media only screen and (min-width: 480px) and (max-width: 767px) {
  .home .site-content {
    margin-top: 12px; }

  .site-content {
    margin-top: 12px; }

  .content-loop .entry-title,
  .content-block-1 .hentry .entry-title {
    font-size: 1.05em; }

  .content-list .thumbnail-link {
    width: 80px;
    margin: 0 15px 15px 0; }
  .content-list .entry-title {
    font-size: 1.2em;
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2; }

  .single h1.entry-title {
    font-size: 26px; } }
/* Mobile Portrait Size to Mobile Landscape Size (devices and browsers) */
@media only screen and (max-width: 479px) {
  .home .site-content {
    margin-top: 10px; }

  .site-content {
    margin-top: 10px; }

  .content-loop {
    margin-left: 0;
    margin-right: 0; }

  .content-loop .entry-title,
  .content-block-1 .hentry .entry-title {
    font-size: 1em; }

  .content-loop .hentry,
  .content-block-1 .hentry {
    margin-bottom: 20px; }

  .breadcrumbs h1 {
    font-size: 1.2em; }

  .archive .breadcrumbs,
  .search .breadcrumbs {
    margin-top: 5px; }

  .breadcrumbs .breadcrumbs-nav {
    margin-bottom: 10px; }

  .single #primary .entry-header {
    margin-bottom: 15px; }
    .single #primary .entry-header .entry-author {
      display: none; }
    .single #primary .entry-header h1.entry-title {
      font-size: 1.1em; }
    .single #primary .entry-header .entry-meta .entry-comment,
    .single #primary .entry-header .entry-meta .sep {
      display: none; }
  .single .has-embed .entry-header {
    margin-top: -5px; }

  .page-content p,
  .entry-content p {
    margin-bottom: 20px; }

  .page-title,
  .single h1.entry-title,
  .page h1.entry-title {
    font-size: 1.2em; }

  #comments .comment-metadata,
  .comment-respond .comment-metadata {
    display: none; }

  .pagination .page-numbers {
    background: none;
    border: none;
    box-shadow: none;
    padding: 0 5px;
    font-size: 13px;
    height: 32px;
    line-height: 32px; }
    .pagination .page-numbers:hover {
      background: none;
      box-shadow: none; } }

/*# sourceMappingURL=responsive.css.map */
