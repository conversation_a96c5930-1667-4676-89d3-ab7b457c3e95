<?php
/**
 * BlackVideo functions and definitions.
 *
 * @link https://developer.wordpress.org/themes/basics/theme-functions/
 *
 * @package blackvideo
 */

if ( ! function_exists( 'blackvideo_setup' ) ) :

function blackvideo_setup() {

	// Load theme textdomain first
	load_theme_textdomain( 'blackvideo', get_template_directory() . '/languages' );

	add_theme_support( "wp-block-styles" );
	add_theme_support( "responsive-embeds" );
	add_theme_support( "align-wide" );

	// Add default posts and comments RSS feed links to head.
	add_theme_support( 'automatic-feed-links' );

	/*
	 * Let WordPress manage the document title.
	 * By adding theme support, we declare that this theme does not use a
	 * hard-coded <title> tag in the document head, and expect WordPress to
	 * provide it for us.
	 */
	add_theme_support( 'title-tag' );

	// Add theme support for Custom Logo.
	// Custom logo.
	$logo_width  = 300;
	$logo_height = 90;

	// If the retina setting is active, double the recommended width and height.
	if ( get_theme_mod( 'retina_logo', false ) ) {
		$logo_width  = floor( $logo_width * 2 );
		$logo_height = floor( $logo_height * 2 );
	}

	$args = array(
		'height'      => $logo_height,
		'width'       => $logo_width,
		'flex-height' => true,
		'flex-width'  => true,
	);

	add_theme_support('custom-logo', $args);

	/*
	 * Enable support for Post Thumbnails on posts and pages.
	 *
	 * @link https://developer.wordpress.org/themes/functionality/featured-images-post-thumbnails/
	 */
	add_theme_support( 'post-thumbnails' );

	// This theme uses wp_nav_menu() in one location.
	register_nav_menus( array(
		'primary' => esc_html__( 'Top Menu', 'blackvideo' ),
		'left' => esc_html__( 'Left Menu', 'blackvideo' ),		
		'footer' => esc_html__( 'Footer Menu', 'blackvideo' ),	
		'mobile' => esc_html__( 'Mobile Menu', 'blackvideo' ),						
	) );

	/*
	 * Switch default core markup for search form, comment form, and comments
	 * to output valid HTML5.
	 */
	add_theme_support( 'html5', array(
		'search-form',
		'comment-form',
		'comment-list',
		'gallery',
		'caption',
	) );

	// Set up the WordPress core custom background feature.
	add_theme_support( 'custom-background', apply_filters( 'blackvideo_custom_background_args', array(
		'default-color' => 'ffffff',
		'default-image' => '',
	) ) );

	// Add support for editor styles.
	add_theme_support( 'editor-styles' );

	$editor_stylesheet_path = './assets/css/editor-style.css';

	// Enqueue editor styles.
	add_editor_style( $editor_stylesheet_path );  

}
endif;

add_action( 'after_setup_theme', 'blackvideo_setup' );



/**
 * Enqueue filter tabs JavaScript
 */
function blackvideo_enqueue_filter_scripts() {
	wp_enqueue_script( 'blackvideo-filter-tabs', get_template_directory_uri() . '/assets/js/filter-tabs.js', array( 'jquery' ), '1.0.0', true );

	// Localize script for AJAX
	wp_localize_script( 'blackvideo-filter-tabs', 'blackvideo_ajax', array(
		'ajax_url' => admin_url( 'admin-ajax.php' ),
		'nonce'    => wp_create_nonce( 'blackvideo_filter_nonce' )
	) );
}
add_action( 'wp_enqueue_scripts', 'blackvideo_enqueue_filter_scripts' );

/**
 * AJAX handler for filtering posts
 */
function blackvideo_filter_posts() {
	// Verify nonce
	if ( ! wp_verify_nonce( $_POST['nonce'], 'blackvideo_filter_nonce' ) ) {
		wp_die( 'Security check failed' );
	}

	$filter = sanitize_text_field( $_POST['filter'] );
	$posts_per_page = 24; // Number of posts to show

	// Set up query arguments based on filter
	$args = array(
		'post_type' => 'post',
		'post_status' => 'publish',
		'posts_per_page' => $posts_per_page,
		'meta_query' => array(),
	);

	switch ( $filter ) {
		case 'most-viewed':
			$args['meta_key'] = 'post_views_count';
			$args['orderby'] = 'meta_value_num';
			$args['order'] = 'DESC';
			break;

		case 'longest':
			$args['meta_key'] = 'video_duration';
			$args['orderby'] = 'meta_value_num';
			$args['order'] = 'DESC';
			break;

		case 'popular':
			$args['meta_key'] = 'post_likes';
			$args['orderby'] = 'meta_value_num';
			$args['order'] = 'DESC';
			break;

		case 'random':
			$args['orderby'] = 'rand';
			break;

		case 'latest':
		default:
			$args['orderby'] = 'date';
			$args['order'] = 'DESC';
			break;
	}

	$query = new WP_Query( $args );

	if ( $query->have_posts() ) {
		ob_start();

		while ( $query->have_posts() ) {
			$query->the_post();
			get_template_part( 'template-parts/content', 'loop' );
		}

		wp_reset_postdata();
		$content = ob_get_clean();

		wp_send_json_success( $content );
	} else {
		wp_send_json_error( 'No posts found' );
	}
}
add_action( 'wp_ajax_blackvideo_filter_posts', 'blackvideo_filter_posts' );
add_action( 'wp_ajax_nopriv_blackvideo_filter_posts', 'blackvideo_filter_posts' );

/**
 * Generate random metadata for demo purposes
 */
function blackvideo_generate_demo_metadata( $post_id ) {
	// Generate random view count
	if ( ! get_post_meta( $post_id, 'post_views_count', true ) ) {
		$views = rand( 1000, 50000 );
		update_post_meta( $post_id, 'post_views_count', $views );
	}

	// Generate random video duration (in seconds)
	if ( ! get_post_meta( $post_id, 'video_duration', true ) ) {
		$duration = rand( 180, 900 ); // 3-15 minutes in seconds
		update_post_meta( $post_id, 'video_duration', $duration );
	}

	// Generate random likes count
	if ( ! get_post_meta( $post_id, 'post_likes', true ) ) {
		$likes = rand( 50, 5000 );
		update_post_meta( $post_id, 'post_likes', $likes );
	}
}

/**
 * Auto-generate metadata for existing posts
 */
function blackvideo_init_demo_metadata() {
	$posts = get_posts( array(
		'numberposts' => 50,
		'post_status' => 'publish'
	) );

	foreach ( $posts as $post ) {
		blackvideo_generate_demo_metadata( $post->ID );
	}
}

// Generate metadata on theme activation or when visiting admin
add_action( 'admin_init', 'blackvideo_init_demo_metadata' );

/**
 * Generate metadata for new posts
 */
function blackvideo_generate_metadata_for_new_post( $post_id ) {
	if ( get_post_type( $post_id ) === 'post' ) {
		blackvideo_generate_demo_metadata( $post_id );
	}
}
add_action( 'publish_post', 'blackvideo_generate_metadata_for_new_post' );

/**
 * Set the content width in pixels, based on the theme's design and stylesheet.
 *
 * Priority 0 to make it available to lower priority callbacks.
 *
 */
// Set content-width.
global $content_width;

if ( ! isset( $content_width ) ) {
	$content_width = 858;
}

/**
 * Register widget area.
 *
 * @link https://developer.wordpress.org/themes/functionality/sidebars/#registering-a-sidebar
 */
function blackvideo_sidebar_init() {

	register_sidebar( array(
		'name'          => esc_html__( 'Sidebar', 'blackvideo' ),
		'id'            => 'sidebar-1',
		'description'   => esc_html__( 'Add widgets here.', 'blackvideo' ),
		'before_widget' => '<div id="%1$s" class="widget %2$s">',
		'after_widget'  => '</div>',
		'before_title'  => '<h2 class="widget-title"><span>',
		'after_title'   => '</span></h2>',
	) );

	register_sidebar( array(
		'name'          => esc_html__( 'Home Content', 'blackvideo' ),
		'id'            => 'home',
		'description'   => esc_html__( 'Only add the "Home Content", "Image" and "Custom HTML" widgets here.', 'blackvideo' ),
		'before_widget' => '<div id="%1$s" class="widget %2$s">',
		'after_widget'  => '</div>',
		'before_title'  => '<h2 class="widget-title"><span>',
		'after_title'   => '</span></h2>',
	) );	

}
add_action( 'widgets_init', 'blackvideo_sidebar_init' );

/**
 * Implement the Custom Header feature.
 */
require get_template_directory() . '/inc/custom-header.php';

/**
 * Custom template tags for this theme.
 */
require get_template_directory() . '/inc/template-tags.php';

/**
 * Custom functions that act independently of the theme templates.
 */
require get_template_directory() . '/inc/extras.php';

/**
 * Customizer additions.
 */
require get_template_directory() . '/inc/customizer.php';

/**
 * Load Jetpack compatibility file.
 */
require get_template_directory() . '/inc/jetpack.php';

/**
 * SVG Icons.
 */
require get_template_directory() . '/inc/classes/class-blackvideo-svg-icons.php';

/**
 * Menu Walker.
 */
require get_template_directory() . '/inc/classes/class-blackvideo-walker-page.php';

// Block Patterns.
require get_template_directory() . '/inc/block-patterns.php';

// Block Styles.
require get_template_directory() . '/inc/block-styles.php';

/**
 * Load about page.
 */
require get_template_directory() . '/inc/about.php';

/**
 * Google fonts.
 */
if ( ! function_exists( 'blackvideo_fonts_url' ) ) :
	/**
	 * Register Google fonts for BlackVideo
	 *
	 * Create your own blackvideo_fonts_url() function to override in a child theme.
	 *
	 * @since 1.0
	 *
	 * @return string Google fonts URL for the theme.
	 */
	function blackvideo_fonts_url() {
		$fonts_url = '';

		/* Translators: If there are characters in your language that are not
		* supported by Poppins, translate this to 'off'. Do not translate
		* into your own language.
		*/
		$font_families = array( 'Inter:wght@400;600;700' );

		if ( ! empty( $font_families  ) ) {

			$query_args = array(
				'family' => implode( '&family=', $font_families ), //urlencode( implode( '|', $font_families ) ),
				// 'subset' => urlencode( 'latin,latin-ext' ),
				'display' => 'swap',
			);

			$fonts_url = add_query_arg( $query_args, 'https://fonts.googleapis.com/css2' );
		}

		if ( ! class_exists( 'WPTT_WebFont_Loader' ) ) {
			// Load Google fonts from Local.
			require_once get_theme_file_path( 'inc/lib/wptt-webfont-loader.php' );
		}

		return esc_url( wptt_get_webfont_url( $fonts_url ) );
	}
endif;

/**
 * Enqueues scripts and styles.
 */
function blackvideo_scripts() {

    // load jquery if it isn't

    wp_enqueue_script('jquery');

	//  Enqueues Javascripts
	wp_enqueue_script( 'superfish', get_template_directory_uri() . '/assets/js/superfish.js', array(), '', true );
	wp_enqueue_script( 'html5', get_template_directory_uri() . '/assets/js/html5.js', array(), '', true );
	wp_enqueue_script( 'theia-sticky-sidebar', get_template_directory_uri() . '/assets/js/theia-sticky-sidebar.js', array(), '', true );                                          		
    wp_enqueue_script( 'blackvideo-index', get_template_directory_uri() . '/assets/js/index.js', array(), '20240520', true );     
	wp_enqueue_script( 'blackvideo-scrollbar', get_template_directory_uri() . '/assets/js/jquery.mCustomScrollbar.concat.min.js', array(), '20240520', true );                      
	wp_enqueue_script( 'blackvideo-custom', get_template_directory_uri() . '/assets/js/jquery.custom.js', array(), '20240520', true );	

    // Enqueues CSS styles
	wp_enqueue_style( 'blackvideo-fonts', blackvideo_fonts_url(), array(), null );
    wp_enqueue_style( 'blackvideo-style', get_stylesheet_uri(), array(), '20240520' );   
	wp_enqueue_style( 'blackvideo-responsive-style',   get_template_directory_uri() . '/responsive.css', array(), '20240520' );       
    wp_enqueue_style( 'genericons-style',   get_template_directory_uri() . '/genericons/genericons.css' );
	wp_enqueue_style( 'font-awesome-style',   get_template_directory_uri() . '/assets/css/font-awesome.css', array(), '20240520' );       	    
	wp_enqueue_style( 'mCustomScrollbar-style', get_template_directory_uri() . '/assets/css/jquery.mCustomScrollbar.css', array(), '20240520'  );
	
    if ( is_singular() && comments_open() && get_option( 'thread_comments' ) ) {
        wp_enqueue_script( 'comment-reply' );
    }    
}
add_action( 'wp_enqueue_scripts', 'blackvideo_scripts' );

function blackvideo_editor_styles() {
	// Enqueue editor styles.
	add_editor_style(
		array(
			blackvideo_fonts_url(),
		)
	);
}
add_action( 'admin_init', 'blackvideo_editor_styles' );

/**
 * Post Thumbnails.
 */
if ( function_exists( 'add_theme_support' ) ) { 
    add_theme_support( 'post-thumbnails' );
    set_post_thumbnail_size( 300, 300, true ); // default Post Thumbnail dimensions (cropped)
    add_image_size( 'blackvideo_post_thumb', 480, 270, true );
}

/**
 * Registers custom widgets.
 */
function blackvideo_widgets_init() {

	require trailingslashit( get_template_directory() ) . 'inc/widgets/widget-popular.php';
	register_widget( 'BlackVideo_Most_Commented_Widget' );		

	require trailingslashit( get_template_directory() ) . 'inc/widgets/widget-recent.php';
	register_widget( 'BlackVideo_Recent_Widget' );		

	require trailingslashit( get_template_directory() ) . 'inc/widgets/widget-random.php';
	register_widget( 'BlackVideo_Random_Widget' );			

	require trailingslashit( get_template_directory() ) . 'inc/widgets/widget-home-content.php';
	register_widget( 'BlackVideo_Home_Content_Widget' );	
																			
}
add_action( 'widgets_init', 'blackvideo_widgets_init' );
