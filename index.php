<?php get_header(); ?>

	<div class="content-wrap clear full-width-content">

	<div class="home-container clear">

		<div id="primary" class="content-area full-width clear">

			<main id="main" class="site-main clear">

				<?php if ( is_active_sidebar( 'home' ) ) { ?>

				<div id="recent-content">

					<?php dynamic_sidebar( 'home' ); ?>

				</div><!-- #recent-content -->	

				<?php } else { ?>

				<!-- Videos being watched section -->
				<div class="content-block">
					<div class="section-heading">
						<h3><span>Videos being watched</span></h3>
						<div class="section-more-link">
							<a href="<?php echo esc_url( home_url( '/?filter=random' ) ); ?>">More videos</a>
						</div>
					</div>
					<div class="content-loop clear">
						<?php
						if ( have_posts() ) :
							$i = 1;
							/* Show first 8 posts */
							while ( have_posts() && $i <= 8 ) : the_post();
								get_template_part('template-parts/content', 'loop');
								$i++;
							endwhile;
							wp_reset_postdata();
						endif;
						?>
					</div>
				</div>

				<!-- Recently added videos section -->
				<div class="content-block">
					<div class="section-heading">
						<h3><span>Recently added videos</span></h3>
					</div>
					<div class="filter-tabs">
						<?php $current_filter = isset($_GET['filter']) ? sanitize_text_field($_GET['filter']) : 'latest'; ?>
						<ul class="filter-list">
							<li class="<?php echo ($current_filter === 'latest') ? 'active' : ''; ?>"><a href="<?php echo esc_url( add_query_arg( 'filter', 'latest', home_url() ) ); ?>" data-filter="latest">Recently added videos</a></li>
							<li class="<?php echo ($current_filter === 'most-viewed') ? 'active' : ''; ?>"><a href="<?php echo esc_url( add_query_arg( 'filter', 'most-viewed', home_url() ) ); ?>" data-filter="most-viewed">Most viewed videos</a></li>
							<li class="<?php echo ($current_filter === 'longest') ? 'active' : ''; ?>"><a href="<?php echo esc_url( add_query_arg( 'filter', 'longest', home_url() ) ); ?>" data-filter="longest">Longest videos</a></li>
							<li class="<?php echo ($current_filter === 'popular') ? 'active' : ''; ?>"><a href="<?php echo esc_url( add_query_arg( 'filter', 'popular', home_url() ) ); ?>" data-filter="popular">Popular videos</a></li>
							<li class="<?php echo ($current_filter === 'random') ? 'active' : ''; ?>"><a href="<?php echo esc_url( add_query_arg( 'filter', 'random', home_url() ) ); ?>" data-filter="random">Random videos</a></li>
						</ul>
					</div>
					<div id="recent-content" class="content-loop clear">
						<?php
						// Handle filter parameter for non-JS fallback
						$current_filter = isset($_GET['filter']) ? sanitize_text_field($_GET['filter']) : 'latest';

						// Set up filtered query
						$filter_args = array(
							'post_type' => 'post',
							'post_status' => 'publish',
							'posts_per_page' => 24,
						);

						switch ( $current_filter ) {
							case 'most-viewed':
								$filter_args['meta_key'] = 'post_views_count';
								$filter_args['orderby'] = 'meta_value_num';
								$filter_args['order'] = 'DESC';
								break;

							case 'longest':
								$filter_args['meta_key'] = 'video_duration';
								$filter_args['orderby'] = 'meta_value_num';
								$filter_args['order'] = 'DESC';
								break;

							case 'popular':
								$filter_args['meta_key'] = 'post_likes';
								$filter_args['orderby'] = 'meta_value_num';
								$filter_args['order'] = 'DESC';
								break;

							case 'random':
								$filter_args['orderby'] = 'rand';
								break;

							case 'latest':
							default:
								$filter_args['orderby'] = 'date';
								$filter_args['order'] = 'DESC';
								break;
						}

						$filtered_query = new WP_Query( $filter_args );

						if ( $filtered_query->have_posts() ) :
							while ( $filtered_query->have_posts() ) : $filtered_query->the_post();
								get_template_part('template-parts/content', 'loop');
							endwhile;
							wp_reset_postdata();
						else :
							get_template_part( 'template-parts/content', 'none' );
						endif;
						?>
					</div><!-- #recent-content -->
				</div>
				
				<?php get_template_part( 'template-parts/pagination', '' ); ?>

				<?php } ?>							

			</main><!-- .site-main -->

		</div><!-- #primary -->

	</div><!-- .home-container -->

	<?php get_template_part( 'template-parts/site', 'bottom' ); ?>

	</div><!-- .content-wrap -->

<?php get_footer(); ?>
