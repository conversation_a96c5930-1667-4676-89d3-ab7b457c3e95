<?php get_header(); ?>
	
	<?php get_template_part('template-parts/sidebar', 'left'); ?>

	<div class="content-wrap clear">

	<div class="home-container clear">

		<div id="primary" class="content-area full-width clear">

			<main id="main" class="site-main clear">

				<?php if ( is_active_sidebar( 'home' ) ) { ?>

				<div id="recent-content">

					<?php dynamic_sidebar( 'home' ); ?>

				</div><!-- #recent-content -->	

				<?php } else { ?>

				<!-- Videos being watched section -->
				<div class="content-block">
					<div class="section-heading">
						<h3><span>Videos being watched</span></h3>
						<div class="section-more-link">
							<a href="<?php echo esc_url( home_url( '/?filter=random' ) ); ?>">More videos</a>
						</div>
					</div>
					<div class="content-loop clear">
						<?php
						if ( have_posts() ) :
							$i = 1;
							/* Show first 8 posts */
							while ( have_posts() && $i <= 8 ) : the_post();
								get_template_part('template-parts/content', 'loop');
								$i++;
							endwhile;
							wp_reset_postdata();
						endif;
						?>
					</div>
				</div>

				<!-- Recently added videos section -->
				<div class="content-block">
					<div class="section-heading">
						<h3><span>Recently added videos</span></h3>
					</div>
					<div class="filter-tabs">
						<ul class="filter-list">
							<li class="active"><a href="#" data-filter="latest">Recently added videos</a></li>
							<li><a href="#" data-filter="most-viewed">Most viewed videos</a></li>
							<li><a href="#" data-filter="longest">Longest videos</a></li>
							<li><a href="#" data-filter="popular">Popular videos</a></li>
							<li><a href="#" data-filter="random">Random videos</a></li>
						</ul>
					</div>
					<div id="recent-content" class="content-loop clear">
						<?php
						// Reset query and show remaining posts
						if ( have_posts() ) :
							$i = 1;
							/* Start the Loop */
							while ( have_posts() ) : the_post();
								if ( $i > 8 ) { // Skip first 8 posts already shown
									get_template_part('template-parts/content', 'loop');
								}
								$i++;
							endwhile;
						else :
							get_template_part( 'template-parts/content', 'none' );
						endif;
						?>
					</div><!-- #recent-content -->
				</div>
				
				<?php get_template_part( 'template-parts/pagination', '' ); ?>

				<?php } ?>							

			</main><!-- .site-main -->

		</div><!-- #primary -->

	</div><!-- .home-container -->

	<?php get_template_part( 'template-parts/site', 'bottom' ); ?>

	</div><!-- .content-wrap -->

<?php get_footer(); ?>
