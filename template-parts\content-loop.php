<div id="post-<?php the_ID(); ?>" <?php post_class( 'ht_grid_1_4 box-effect' ); ?>>	

	<?php if ( has_post_thumbnail() ) { ?>
		<a class="thumbnail-link" href="<?php the_permalink(); ?>">
			<div class="thumbnail-wrap">
				<?php
					the_post_thumbnail('blackvideo_post_thumb');
				?>
			</div><!-- .thumbnail-wrap -->
			<div class="video-duration">
				<?php
				// Get duration from post meta or generate random for demo
				$duration = get_post_meta(get_the_ID(), 'video_duration', true);
				if (!$duration) {
					$duration = rand(180, 900); // 3-15 minutes in seconds
					update_post_meta(get_the_ID(), 'video_duration', $duration);
				}
				$minutes = floor($duration / 60);
				$seconds = $duration % 60;
				echo sprintf('%02d:%02d', $minutes, $seconds);
				?>
			</div>
			<?php
			// Add premium badge randomly for demo
			if( rand(1, 4) == 1 ) { ?>
				<div class="premium-badge">Premium</div>
			<?php } ?>
			<?php if( (blackvideo_has_embed_code() || blackvideo_has_embed()) ) { ?>
				<div class="icon-play"><i class="genericon genericon-play"></i></div>
			<?php } ?>
		</a>
	<?php } ?>

	<div class="entry-header">

		<?php if (!is_category()) : ?>
			<div class="entry-category"><?php blackvideo_first_category(); ?></div>
		<?php endif; ?>

		<h2 class="entry-title"><a href="<?php the_permalink(); ?>"><?php the_title(); ?></a></h2>

		<div class="entry-meta">
			<span class="entry-views">
				<?php
				// Get view count from post meta or generate for demo
				$views = get_post_meta(get_the_ID(), 'post_views_count', true);
				if (!$views) {
					$views = rand(1000, 50000);
					update_post_meta(get_the_ID(), 'post_views_count', $views);
				}
				echo number_format($views) . ' views';
				?>
			</span>
			<span class="entry-rating">
				<?php
				// Generate rating based on likes/views ratio for demo
				$likes = get_post_meta(get_the_ID(), 'post_likes', true);
				if (!$likes) {
					$likes = rand(50, 5000);
					update_post_meta(get_the_ID(), 'post_likes', $likes);
				}
				$rating = min(99, max(75, round(($likes / $views) * 100 * 10)));
				echo $rating . '%';
				?>
			</span>
		</div><!-- .entry-meta -->
									
	</div><!-- .entry-header -->

</div><!-- #post-<?php the_ID(); ?> -->