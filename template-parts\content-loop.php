<div id="post-<?php the_ID(); ?>" <?php post_class( 'ht_grid_1_4 box-effect' ); ?>>	

	<?php if ( has_post_thumbnail() ) { ?>
		<a class="thumbnail-link" href="<?php the_permalink(); ?>">
			<div class="thumbnail-wrap">
				<?php
					the_post_thumbnail('blackvideo_post_thumb');
				?>
			</div><!-- .thumbnail-wrap -->
			<div class="video-duration">
				<?php
				// Generate random duration for demo purposes
				$minutes = rand(3, 15);
				$seconds = rand(10, 59);
				echo sprintf('%02d:%02d', $minutes, $seconds);
				?>
			</div>
			<?php
			// Add premium badge randomly for demo
			if( rand(1, 4) == 1 ) { ?>
				<div class="premium-badge">Premium</div>
			<?php } ?>
			<?php if( (blackvideo_has_embed_code() || blackvideo_has_embed()) ) { ?>
				<div class="icon-play"><i class="genericon genericon-play"></i></div>
			<?php } ?>
		</a>
	<?php } ?>

	<div class="entry-header">

		<?php if (!is_category()) : ?>
			<div class="entry-category"><?php blackvideo_first_category(); ?></div>
		<?php endif; ?>

		<h2 class="entry-title"><a href="<?php the_permalink(); ?>"><?php the_title(); ?></a></h2>

		<div class="entry-meta">
			<span class="entry-views">
				<?php
				// Generate random view count for demo
				$views = rand(1000, 50000);
				echo number_format($views) . ' views';
				?>
			</span>
			<span class="entry-rating">
				<?php
				// Generate random rating for demo
				$rating = rand(75, 99);
				echo $rating . '%';
				?>
			</span>
		</div><!-- .entry-meta -->
									
	</div><!-- .entry-header -->

</div><!-- #post-<?php the_ID(); ?> -->